"""
Example training script for FastLLaMA.

Demonstrates how to train a FastLLaMA model with the three-phase training strategy,
memory optimizations, and comprehensive metrics tracking.
"""

import os
import torch
import torch.distributed as dist
from torch.utils.data import DataLoader, Dataset
import argparse
import logging
from typing import Dict, List

# FastLLaMA imports
from fastllama import FastLLaMAConfig, FastLLaMAModel, FastLLaMATrainer
from fastllama.training import TrainingArguments, PhaseBasedStrategy
from fastllama.utils import MemoryOptimizer, MetricsCollector


# Simple dataset for demonstration
class DummyTextDataset(Dataset):
    """Dummy text dataset for training demonstration."""
    
    def __init__(self, num_samples: int = 10000, seq_length: int = 2048, vocab_size: int = 32000):
        self.num_samples = num_samples
        self.seq_length = seq_length
        self.vocab_size = vocab_size
    
    def __len__(self):
        return self.num_samples
    
    def __getitem__(self, idx):
        # Generate random token sequences
        input_ids = torch.randint(0, self.vocab_size, (self.seq_length,))
        labels = input_ids.clone()
        attention_mask = torch.ones_like(input_ids)
        
        return {
            "input_ids": input_ids,
            "labels": labels,
            "attention_mask": attention_mask
        }


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('fastllama_training.log'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)


def setup_distributed():
    """Setup distributed training if available."""
    if 'RANK' in os.environ and 'WORLD_SIZE' in os.environ:
        rank = int(os.environ['RANK'])
        world_size = int(os.environ['WORLD_SIZE'])
        local_rank = int(os.environ['LOCAL_RANK'])
        
        dist.init_process_group(backend='nccl', rank=rank, world_size=world_size)
        torch.cuda.set_device(local_rank)
        
        return rank, world_size, local_rank
    else:
        return 0, 1, -1


def create_model_and_config(args) -> tuple:
    """Create FastLLaMA model and configuration."""
    # Create configuration
    config = FastLLaMAConfig(
        hidden_size=args.hidden_size,
        intermediate_size=args.intermediate_size,
        num_attention_heads=args.num_attention_heads,
        num_key_value_heads=args.num_key_value_heads,
        num_hidden_layers=args.num_hidden_layers,
        vocab_size=args.vocab_size,
        max_position_embeddings=args.max_position_embeddings,
        
        # Enable FastLLaMA features
        enable_context_compression=True,
        enable_early_exit=True,
        use_gradient_checkpointing=True,
        use_mixed_precision=True,
        kv_cache_quantization=True,
        parameter_sharing=True,
        
        # Hierarchical attention configuration
        local_attention_window=512,
        sparse_attention_stride=8,
        compression_ratio=20,
        
        # Early exit configuration
        early_exit_layers=[12, 18, 24],
        confidence_threshold=0.8,
    )
    
    # Create model
    model = FastLLaMAModel(config)
    
    logger = logging.getLogger(__name__)
    logger.info(f"Created FastLLaMA model with {sum(p.numel() for p in model.parameters())} parameters")
    
    return model, config


def create_datasets(args) -> tuple:
    """Create training and evaluation datasets."""
    train_dataset = DummyTextDataset(
        num_samples=args.num_train_samples,
        seq_length=args.max_seq_length,
        vocab_size=args.vocab_size
    )
    
    eval_dataset = DummyTextDataset(
        num_samples=args.num_eval_samples,
        seq_length=args.max_seq_length,
        vocab_size=args.vocab_size
    )
    
    return train_dataset, eval_dataset


def main():
    parser = argparse.ArgumentParser(description="Train FastLLaMA model")
    
    # Model configuration
    parser.add_argument("--hidden_size", type=int, default=4096)
    parser.add_argument("--intermediate_size", type=int, default=11008)
    parser.add_argument("--num_attention_heads", type=int, default=32)
    parser.add_argument("--num_key_value_heads", type=int, default=8)
    parser.add_argument("--num_hidden_layers", type=int, default=32)
    parser.add_argument("--vocab_size", type=int, default=32000)
    parser.add_argument("--max_position_embeddings", type=int, default=32768)
    
    # Training configuration
    parser.add_argument("--output_dir", type=str, default="./fastllama_output")
    parser.add_argument("--num_train_epochs", type=int, default=3)
    parser.add_argument("--per_device_train_batch_size", type=int, default=4)
    parser.add_argument("--per_device_eval_batch_size", type=int, default=4)
    parser.add_argument("--gradient_accumulation_steps", type=int, default=1)
    parser.add_argument("--learning_rate", type=float, default=5e-5)
    parser.add_argument("--weight_decay", type=float, default=0.01)
    parser.add_argument("--warmup_steps", type=int, default=1000)
    parser.add_argument("--max_seq_length", type=int, default=8192)
    
    # Dataset configuration
    parser.add_argument("--num_train_samples", type=int, default=10000)
    parser.add_argument("--num_eval_samples", type=int, default=1000)
    
    # Phase-based training
    parser.add_argument("--foundation_phase_ratio", type=float, default=0.7)
    parser.add_argument("--long_context_phase_ratio", type=float, default=0.2)
    parser.add_argument("--efficiency_phase_ratio", type=float, default=0.1)
    
    # Logging and evaluation
    parser.add_argument("--eval_steps", type=int, default=500)
    parser.add_argument("--save_steps", type=int, default=1000)
    parser.add_argument("--logging_steps", type=int, default=100)
    
    args = parser.parse_args()
    
    # Setup logging
    logger = setup_logging()
    logger.info("Starting FastLLaMA training")
    
    # Setup distributed training
    rank, world_size, local_rank = setup_distributed()
    logger.info(f"Distributed training: rank={rank}, world_size={world_size}, local_rank={local_rank}")
    
    # Create model and configuration
    model, config = create_model_and_config(args)
    
    # Create datasets
    train_dataset, eval_dataset = create_datasets(args)
    logger.info(f"Created datasets: train={len(train_dataset)}, eval={len(eval_dataset)}")
    
    # Setup memory optimizer
    memory_optimizer = MemoryOptimizer(model, config)
    memory_optimizer.optimize_for_training()
    logger.info("Memory optimizations applied")
    
    # Setup metrics collector
    metrics_collector = MetricsCollector(config)
    
    # Create training arguments
    training_args = TrainingArguments(
        output_dir=args.output_dir,
        num_train_epochs=args.num_train_epochs,
        per_device_train_batch_size=args.per_device_train_batch_size,
        per_device_eval_batch_size=args.per_device_eval_batch_size,
        gradient_accumulation_steps=args.gradient_accumulation_steps,
        learning_rate=args.learning_rate,
        weight_decay=args.weight_decay,
        warmup_steps=args.warmup_steps,
        
        # Memory optimizations
        use_mixed_precision=True,
        gradient_checkpointing=True,
        max_grad_norm=1.0,
        
        # Sequence length scheduling
        initial_seq_length=2048,
        max_seq_length=args.max_seq_length,
        seq_length_warmup_steps=5000,
        
        # Evaluation and logging
        eval_steps=args.eval_steps,
        save_steps=args.save_steps,
        logging_steps=args.logging_steps,
        
        # Phase-based training
        foundation_phase_ratio=args.foundation_phase_ratio,
        long_context_phase_ratio=args.long_context_phase_ratio,
        efficiency_phase_ratio=args.efficiency_phase_ratio,
        
        # Hardware settings
        dataloader_num_workers=4,
        local_rank=local_rank,
    )
    
    # Create trainer
    trainer = FastLLaMATrainer(
        model=model,
        config=config,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=eval_dataset,
    )
    
    logger.info("Starting training...")
    
    try:
        # Train the model
        training_metrics = trainer.train()
        
        logger.info("Training completed successfully!")
        logger.info(f"Final metrics: {training_metrics}")
        
        # Save final metrics
        metrics_collector.save_metrics(args.output_dir)
        
        # Print memory usage summary
        memory_stats = memory_optimizer.get_memory_usage()
        logger.info(f"Final memory usage: {memory_stats}")
        
    except Exception as e:
        logger.error(f"Training failed with error: {e}")
        raise
    
    finally:
        # Cleanup
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        if world_size > 1:
            dist.destroy_process_group()


if __name__ == "__main__":
    main()
