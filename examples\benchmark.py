"""
Comprehensive benchmark script for FastLLaMA.

Compares FastLLaMA performance against standard LLaMA across different
sequence lengths, batch sizes, and model configurations.
"""

import torch
import time
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import argparse
import logging
from typing import Dict, List, Tuple, Any
import json
import os

# FastLLaMA imports
from fastllama import FastLLaMAConfig, FastLLaMAModel, FastLLaMAInferenceEngine
from fastllama.inference import GenerationConfig
from fastllama.utils import get_memory_stats, MemoryOptimizer


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


class BenchmarkSuite:
    """Comprehensive benchmark suite for FastLLaMA."""
    
    def __init__(self, config: FastLLaMAConfig, device: torch.device):
        self.config = config
        self.device = device
        self.logger = logging.getLogger(__name__)
        self.results = {}
        
    def create_model(self, enable_optimizations: bool = True) -> FastLLaMAModel:
        """Create FastLLaMA model with or without optimizations."""
        config = FastLLaMAConfig(**self.config.to_dict())
        
        if not enable_optimizations:
            # Disable FastLLaMA optimizations for baseline comparison
            config.enable_context_compression = False
            config.enable_early_exit = False
            config.kv_cache_quantization = False
            config.use_gradient_checkpointing = False
            config.parameter_sharing = False
        
        self.logger.info("Creating model...")
        model = FastLLaMAModel(config)
        model.to(self.device)
        model.eval()
        num_params = sum(p.numel() for p in model.parameters())
        self.logger.info(f"Created model with {num_params/1e9:.2f}B parameters")
        return model
    
    def generate_test_inputs(self, batch_size: int, seq_length: int) -> torch.Tensor:
        """Generate random test inputs."""
        return torch.randint(
            0, self.config.vocab_size, 
            (batch_size, seq_length), 
            device=self.device
        )
    
    def benchmark_inference_speed(
        self, 
        sequence_lengths: List[int],
        batch_sizes: List[int],
        num_runs: int = 5
    ) -> Dict[str, Any]:
        """Benchmark inference speed across different configurations."""
        self.logger.info("Running inference speed benchmark...")
        
        results = {
            "fastllama": {},
            "baseline": {},
            "speedup": {}
        }
        
        for seq_len in sequence_lengths:
            for batch_size in batch_sizes:
                self.logger.info(f"Testing seq_len={seq_len}, batch_size={batch_size}")
                
                # Test FastLLaMA with optimizations
                fastllama_model = self.create_model(enable_optimizations=True)
                fastllama_engine = FastLLaMAInferenceEngine(fastllama_model, self.config, self.device)
                
                fastllama_times = []
                for _ in range(num_runs):
                    input_ids = self.generate_test_inputs(batch_size, seq_len)
                    
                    torch.cuda.synchronize() if torch.cuda.is_available() else None
                    start_time = time.time()
                    
                    _ = fastllama_engine.generate(
                        input_ids=input_ids,
                        generation_config=GenerationConfig(max_new_tokens=50, do_sample=False)
                    )
                    
                    torch.cuda.synchronize() if torch.cuda.is_available() else None
                    end_time = time.time()
                    
                    fastllama_times.append(end_time - start_time)
                
                # Test baseline (FastLLaMA without optimizations)
                baseline_model = self.create_model(enable_optimizations=False)
                baseline_engine = FastLLaMAInferenceEngine(baseline_model, self.config, self.device)
                
                baseline_times = []
                for _ in range(num_runs):
                    input_ids = self.generate_test_inputs(batch_size, seq_len)
                    
                    torch.cuda.synchronize() if torch.cuda.is_available() else None
                    start_time = time.time()
                    
                    _ = baseline_engine.generate(
                        input_ids=input_ids,
                        generation_config=GenerationConfig(max_new_tokens=50, do_sample=False)
                    )
                    
                    torch.cuda.synchronize() if torch.cuda.is_available() else None
                    end_time = time.time()
                    
                    baseline_times.append(end_time - start_time)
                
                # Calculate statistics
                fastllama_avg = np.mean(fastllama_times)
                baseline_avg = np.mean(baseline_times)
                speedup = baseline_avg / fastllama_avg
                
                key = f"seq{seq_len}_batch{batch_size}"
                results["fastllama"][key] = {
                    "avg_time": fastllama_avg,
                    "std_time": np.std(fastllama_times),
                    "times": fastllama_times
                }
                results["baseline"][key] = {
                    "avg_time": baseline_avg,
                    "std_time": np.std(baseline_times),
                    "times": baseline_times
                }
                results["speedup"][key] = speedup
                
                self.logger.info(f"FastLLaMA: {fastllama_avg:.3f}s, Baseline: {baseline_avg:.3f}s, Speedup: {speedup:.2f}x")
                
                # Cleanup
                del fastllama_model, baseline_model, fastllama_engine, baseline_engine
                torch.cuda.empty_cache() if torch.cuda.is_available() else None
        
        return results
    
    def benchmark_memory_usage(
        self,
        sequence_lengths: List[int],
        batch_sizes: List[int]
    ) -> Dict[str, Any]:
        """Benchmark memory usage across different configurations."""
        self.logger.info("Running memory usage benchmark...")
        
        results = {
            "fastllama": {},
            "baseline": {},
            "memory_savings": {}
        }
        
        for seq_len in sequence_lengths:
            for batch_size in batch_sizes:
                self.logger.info(f"Testing memory for seq_len={seq_len}, batch_size={batch_size}")
                
                # Test FastLLaMA memory usage
                torch.cuda.empty_cache() if torch.cuda.is_available() else None
                torch.cuda.reset_peak_memory_stats() if torch.cuda.is_available() else None
                
                fastllama_model = self.create_model(enable_optimizations=True)
                fastllama_engine = FastLLaMAInferenceEngine(fastllama_model, self.config, self.device)
                
                input_ids = self.generate_test_inputs(batch_size, seq_len)
                _ = fastllama_engine.generate(
                    input_ids=input_ids,
                    generation_config=GenerationConfig(max_new_tokens=50, do_sample=False)
                )
                
                fastllama_memory = get_memory_stats()
                
                # Test baseline memory usage
                del fastllama_model, fastllama_engine
                torch.cuda.empty_cache() if torch.cuda.is_available() else None
                torch.cuda.reset_peak_memory_stats() if torch.cuda.is_available() else None
                
                baseline_model = self.create_model(enable_optimizations=False)
                baseline_engine = FastLLaMAInferenceEngine(baseline_model, self.config, self.device)
                
                input_ids = self.generate_test_inputs(batch_size, seq_len)
                _ = baseline_engine.generate(
                    input_ids=input_ids,
                    generation_config=GenerationConfig(max_new_tokens=50, do_sample=False)
                )
                
                baseline_memory = get_memory_stats()
                
                # Calculate memory savings
                fastllama_peak = fastllama_memory.get("gpu_max_allocated_gb", 0)
                baseline_peak = baseline_memory.get("gpu_max_allocated_gb", 0)
                memory_savings = (baseline_peak - fastllama_peak) / baseline_peak if baseline_peak > 0 else 0
                
                key = f"seq{seq_len}_batch{batch_size}"
                results["fastllama"][key] = fastllama_memory
                results["baseline"][key] = baseline_memory
                results["memory_savings"][key] = memory_savings
                
                self.logger.info(f"FastLLaMA: {fastllama_peak:.2f}GB, Baseline: {baseline_peak:.2f}GB, Savings: {memory_savings:.1%}")
                
                # Cleanup
                del baseline_model, baseline_engine
                torch.cuda.empty_cache() if torch.cuda.is_available() else None
        
        return results
    
    def benchmark_early_exit_effectiveness(
        self,
        sequence_lengths: List[int],
        confidence_thresholds: List[float]
    ) -> Dict[str, Any]:
        """Benchmark early exit effectiveness."""
        self.logger.info("Running early exit effectiveness benchmark...")
        
        results = {
            "exit_rates": {},
            "speedups": {},
            "confidence_scores": {}
        }
        
        model = self.create_model(enable_optimizations=True)
        engine = FastLLaMAInferenceEngine(model, self.config, self.device)
        
        for seq_len in sequence_lengths:
            for threshold in confidence_thresholds:
                self.logger.info(f"Testing early exit for seq_len={seq_len}, threshold={threshold}")
                
                generation_config = GenerationConfig(
                    max_new_tokens=50,
                    enable_early_exit=True,
                    confidence_threshold=threshold,
                    do_sample=False
                )
                
                exit_info_list = []
                generation_stats_list = []
                
                # Run multiple tests
                for _ in range(10):
                    input_ids = self.generate_test_inputs(1, seq_len)
                    outputs = engine.generate(input_ids=input_ids, generation_config=generation_config)
                    
                    if outputs.early_exit_info:
                        exit_info_list.append(outputs.early_exit_info)
                    if outputs.generation_stats:
                        generation_stats_list.append(outputs.generation_stats)
                
                # Calculate statistics
                if exit_info_list:
                    exit_rates = []
                    confidence_scores = []
                    
                    for info in exit_info_list:
                        exits_used = info.get("exits_used", [])
                        confidences = info.get("confidence_scores", [])
                        
                        exit_rate = len(exits_used) / 50 if exits_used else 0  # 50 is max_new_tokens
                        exit_rates.append(exit_rate)
                        confidence_scores.extend(confidences)
                    
                    avg_exit_rate = np.mean(exit_rates)
                    avg_confidence = np.mean(confidence_scores) if confidence_scores else 0
                    
                    # Estimate speedup based on early exits
                    avg_speedup = np.mean([stats.get("early_exit_usage", 0) for stats in generation_stats_list])
                    
                    key = f"seq{seq_len}_thresh{threshold}"
                    results["exit_rates"][key] = avg_exit_rate
                    results["speedups"][key] = avg_speedup
                    results["confidence_scores"][key] = avg_confidence
                    
                    self.logger.info(f"Exit rate: {avg_exit_rate:.2%}, Confidence: {avg_confidence:.3f}, Speedup: {avg_speedup:.2f}")
        
        return results
    
    def run_full_benchmark(self, args) -> Dict[str, Any]:
        """Run the complete benchmark suite."""
        self.logger.info("Starting comprehensive FastLLaMA benchmark...")
        
        # Define test configurations
        sequence_lengths = args.sequence_lengths
        batch_sizes = args.batch_sizes
        confidence_thresholds = args.confidence_thresholds
        
        # Run benchmarks
        speed_results = self.benchmark_inference_speed(sequence_lengths, batch_sizes, args.num_runs)
        memory_results = self.benchmark_memory_usage(sequence_lengths, batch_sizes)
        early_exit_results = self.benchmark_early_exit_effectiveness(sequence_lengths, confidence_thresholds)
        
        # Combine results
        all_results = {
            "speed": speed_results,
            "memory": memory_results,
            "early_exit": early_exit_results,
            "config": self.config.to_dict(),
            "test_config": {
                "sequence_lengths": sequence_lengths,
                "batch_sizes": batch_sizes,
                "confidence_thresholds": confidence_thresholds,
                "num_runs": args.num_runs,
                "device": str(self.device)
            }
        }
        
        return all_results
    
    def save_results(self, results: Dict[str, Any], output_dir: str):
        """Save benchmark results to files."""
        os.makedirs(output_dir, exist_ok=True)
        
        # Save raw results
        with open(os.path.join(output_dir, "benchmark_results.json"), 'w') as f:
            json.dump(results, f, indent=2)
        
        # Create summary report
        self.create_summary_report(results, output_dir)
        
        # Create visualizations
        self.create_visualizations(results, output_dir)
    
    def create_summary_report(self, results: Dict[str, Any], output_dir: str):
        """Create a summary report of benchmark results."""
        report_path = os.path.join(output_dir, "benchmark_summary.txt")
        
        with open(report_path, 'w') as f:
            f.write("FastLLaMA Benchmark Summary\n")
            f.write("=" * 50 + "\n\n")
            
            # Speed results summary
            f.write("Inference Speed Results:\n")
            f.write("-" * 25 + "\n")
            speed_results = results["speed"]["speedup"]
            avg_speedup = np.mean(list(speed_results.values()))
            max_speedup = max(speed_results.values())
            f.write(f"Average speedup: {avg_speedup:.2f}x\n")
            f.write(f"Maximum speedup: {max_speedup:.2f}x\n\n")
            
            # Memory results summary
            f.write("Memory Usage Results:\n")
            f.write("-" * 22 + "\n")
            memory_results = results["memory"]["memory_savings"]
            avg_savings = np.mean(list(memory_results.values()))
            max_savings = max(memory_results.values())
            f.write(f"Average memory savings: {avg_savings:.1%}\n")
            f.write(f"Maximum memory savings: {max_savings:.1%}\n\n")
            
            # Early exit results summary
            f.write("Early Exit Results:\n")
            f.write("-" * 19 + "\n")
            exit_results = results["early_exit"]["exit_rates"]
            if exit_results:
                avg_exit_rate = np.mean(list(exit_results.values()))
                f.write(f"Average early exit rate: {avg_exit_rate:.1%}\n")
        
        self.logger.info(f"Summary report saved to {report_path}")
    
    def create_visualizations(self, results: Dict[str, Any], output_dir: str):
        """Create visualization plots for benchmark results."""
        try:
            # Speed comparison plot
            self.plot_speed_comparison(results["speed"], output_dir)
            
            # Memory usage plot
            self.plot_memory_usage(results["memory"], output_dir)
            
            # Early exit effectiveness plot
            self.plot_early_exit_effectiveness(results["early_exit"], output_dir)
            
        except Exception as e:
            self.logger.warning(f"Failed to create visualizations: {e}")
    
    def plot_speed_comparison(self, speed_results: Dict[str, Any], output_dir: str):
        """Plot speed comparison between FastLLaMA and baseline."""
        speedups = list(speed_results["speedup"].values())
        configs = list(speed_results["speedup"].keys())
        
        plt.figure(figsize=(12, 6))
        plt.bar(range(len(speedups)), speedups)
        plt.xlabel("Configuration")
        plt.ylabel("Speedup (x)")
        plt.title("FastLLaMA vs Baseline Speed Comparison")
        plt.xticks(range(len(configs)), configs, rotation=45)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, "speed_comparison.png"))
        plt.close()
    
    def plot_memory_usage(self, memory_results: Dict[str, Any], output_dir: str):
        """Plot memory usage comparison."""
        savings = list(memory_results["memory_savings"].values())
        configs = list(memory_results["memory_savings"].keys())
        
        plt.figure(figsize=(12, 6))
        plt.bar(range(len(savings)), [s * 100 for s in savings])
        plt.xlabel("Configuration")
        plt.ylabel("Memory Savings (%)")
        plt.title("FastLLaMA Memory Savings")
        plt.xticks(range(len(configs)), configs, rotation=45)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, "memory_savings.png"))
        plt.close()
    
    def plot_early_exit_effectiveness(self, early_exit_results: Dict[str, Any], output_dir: str):
        """Plot early exit effectiveness."""
        if not early_exit_results["exit_rates"]:
            return
        
        exit_rates = list(early_exit_results["exit_rates"].values())
        configs = list(early_exit_results["exit_rates"].keys())
        
        plt.figure(figsize=(12, 6))
        plt.bar(range(len(exit_rates)), [r * 100 for r in exit_rates])
        plt.xlabel("Configuration")
        plt.ylabel("Early Exit Rate (%)")
        plt.title("Early Exit Effectiveness")
        plt.xticks(range(len(configs)), configs, rotation=45)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, "early_exit_effectiveness.png"))
        plt.close()


def main():
    parser = argparse.ArgumentParser(description="FastLLaMA Comprehensive Benchmark")
    
    # Model configuration
    parser.add_argument("--hidden_size", type=int, default=2048)
    parser.add_argument("--num_attention_heads", type=int, default=16)
    parser.add_argument("--num_key_value_heads", type=int, default=4)
    parser.add_argument("--num_hidden_layers", type=int, default=16)
    parser.add_argument("--vocab_size", type=int, default=32000)
    
    # Benchmark configuration
    parser.add_argument("--sequence_lengths", nargs="+", type=int, default=[512, 1024, 2048, 4096])
    parser.add_argument("--batch_sizes", nargs="+", type=int, default=[1, 2, 4])
    parser.add_argument("--confidence_thresholds", nargs="+", type=float, default=[0.7, 0.8, 0.9])
    parser.add_argument("--num_runs", type=int, default=5)
    
    # Output configuration
    parser.add_argument("--output_dir", type=str, default="./benchmark_results")
    
    args = parser.parse_args()
    
    # Setup logging
    logger = setup_logging()
    logger.info("Starting FastLLaMA benchmark suite")
    
    # Setup device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logger.info(f"Using device: {device}")
    
    # Create configuration
    config = FastLLaMAConfig(
        device=device,
        hidden_size=args.hidden_size,
        num_attention_heads=args.num_attention_heads,
        num_key_value_heads=args.num_key_value_heads,
        num_hidden_layers=args.num_hidden_layers,
        vocab_size=args.vocab_size,
        early_exit_layers = [2, 4, 8],
        
        # Enable all FastLLaMA features for benchmarking
        enable_context_compression=True,
        enable_early_exit=True,
        kv_cache_quantization=True,
        use_gradient_checkpointing=True,
        parameter_sharing=True,
        enable_speculative_decoding=True,
    )
    
    # Create benchmark suite
    benchmark = BenchmarkSuite(config, device)
    
    # Run benchmarks
    results = benchmark.run_full_benchmark(args)
    
    # Save results
    benchmark.save_results(results, args.output_dir)
    
    logger.info(f"Benchmark completed! Results saved to {args.output_dir}")


if __name__ == "__main__":
    main()
